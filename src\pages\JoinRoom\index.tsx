import React, { FC, useContext, useState } from 'react';
import styled from 'styled-components';
import { StoreValue } from 'rc-field-form/lib/interface';

import { Input, Button, Form, Modal } from 'antd';
import { Context } from '../../context';
import { setSessionInfo } from '../../utils/index';
import axios from 'axios';

// Import character images
import Character0 from '../../assets/images/characters/character0.png';
import Character1 from '../../assets/images/characters/character_1.png';
import Character2 from '../../assets/images/characters/character_2.png';
import Character3 from '../../assets/images/characters/character_3.png';
import MagicCall from '../../assets/images/magicCall.png';


const ModalWrapper = styled(Modal)`
  .ant-modal-header {
    border-bottom: none;
    padding: 15px 32px 0px;
  }
  .ant-modal-body {
    padding: 14px 32px 24px;
  }
  .ant-modal-content {
    height: auto;
    min-height: 300px;
    border-radius: 8px;
  }
  .ant-modal-title {
    font-family: PingFang SC;
    font-size: 32px;
    font-style: normal;
    font-weight: 600;
    line-height: 80px;
    text-align: left;
    height: 80px;
  }
  .ant-modal-header {
    border-radius: 6px 6px 0 0;
  }
`;

const TypeRadio = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`;



const CharacterCategoryTitle = styled.h3`
  font-size: 18px;
  margin: 15px 0 10px;
  color: #1890ff;
  font-weight: 600;
  position: relative;
  padding-left: 12px;

  &:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 18px;
    background-color: #1890ff;
    border-radius: 2px;
  }
`;

const CharacterCardsContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: center;
  margin-bottom: 20px;
  padding: 5px;
`;

const CharacterCard = styled.div<{ isSelected: boolean }>`
  width: 180px;
  height: 180px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  border: 3px solid ${props => props.isSelected ? '#1890ff' : 'transparent'};
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }

  &:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: ${props => props.isSelected ? 'rgba(24, 144, 255, 0.1)' : 'transparent'};
    z-index: 1;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`;

const CharacterName = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 5px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  z-index: 2;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
`;

const FormContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
`;

enum ERROR_TYPES {
  VALID,
  EMPTY_STRING,
  INVALID_CHARACTERS,
}

const messages = {
  userIdErrType: {
    1: '请填写用户Id',
    2: '用户Id输入有误，请重新输入',
  },
  roomIdErrType: {
    1: '请填写房间Id',
    2: '房间Id输入有误，请重新输入',
  },
};

const getLoginFieldRules = (
  value: StoreValue,
  name: 'userId' | 'roomId',
  regRes: boolean
): Promise<void | Error> => {
  const errorTypeKey = name === 'userId' ? 'userIdErrType' : 'roomIdErrType';

  if (!value || regRes) {
    const _value = !value ? ERROR_TYPES.EMPTY_STRING : ERROR_TYPES.INVALID_CHARACTERS;
    return Promise.reject(new Error(messages[errorTypeKey][_value]));
  } else {
    return Promise.resolve();
  }
};

// Define character data
// Add a type declaration for imported images
type ImportedImage = any; // This is a workaround for TypeScript image import issues

interface Character {
  id: number;
  name: string;
  image: ImportedImage;
  type: 'roleplay' | 'scenario';
}

const characters: Character[] = [
  { id: 0, name: '知心姐姐', image: Character0, type: 'roleplay' },
  { id: 1, name: '搞笑姐姐', image: Character1, type: 'roleplay' },
  { id: 2, name: '与台湾妹子共处一室', image: Character2, type: 'scenario' },
  { id: 3, name: '跟长沙妹子一起出去旅行', image: Character3, type: 'scenario' },
];

const JoinRoom: FC<{ joinRoom: () => void }> = ({ joinRoom }) => {
  const [form] = Form.useForm();
  const [serverType, setServerType] = useState<number>(0);

  const { setRoomId, setUserId, userId, roomId, joinFailReason, characterType, setCharacterType } = useContext(Context);

  const onRadioChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setServerType(e.target.value === 'translate' ? 1 : 0);
  };

  const handleCharacterSelect = (id: number) => {
    setCharacterType(id);
  };

  const onFinish = async () => {

    const RandUserId = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    const RandRoomId = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);

    setUserId(RandUserId);
    setRoomId(RandRoomId);
    axios.post('https://iyuuki.xyz/rtc/api/start_voice_chat', {
      room_id: RandRoomId,
      user_id: RandUserId,
      lang: 'zh',
      is_tuya: 2,
      is_translate: `${serverType}`,
      character_type: characterType,
    });
    joinRoom();
    setSessionInfo({ RandRoomId, uid: RandUserId });
    window.history.replaceState('', '', `/?userId=${RandUserId}&roomId=${RandRoomId}&serverType=${serverType}&characterType=${characterType}&lang=${'en'}`);
  };


  return (
    <>
      <ModalWrapper width={600} visible={true} closable={false} footer={null} centered>
        {/* Character Selection */}
        {
          false &&
          <div style={{ marginTop: 20, marginBottom: 20, background: '#f5f5f5', padding: '15px', borderRadius: '8px' }}>
            <h2 style={{ textAlign: 'center', marginBottom: '15px', color: '#333', fontSize: '18px', fontWeight: 'bold' }}>请选择一个角色或剧情</h2>
            <CharacterCategoryTitle>角色扮演</CharacterCategoryTitle>
            <CharacterCardsContainer>
              {characters.filter(char => char.type === 'roleplay').map(char => (
                <div key={char.id} style={{ position: 'relative' }}>
                  <CharacterCard
                    isSelected={characterType === char.id}
                    onClick={() => handleCharacterSelect(char.id)}
                  >
                    <img src={typeof char.image === 'string' ? char.image : char.image.default} alt={char.name} />
                  </CharacterCard>
                  <CharacterName>{char.name}</CharacterName>
                </div>
              ))}
            </CharacterCardsContainer>

            <CharacterCategoryTitle>剧情演绎</CharacterCategoryTitle>
            <CharacterCardsContainer>
              {characters.filter(char => char.type === 'scenario').map(char => (
                <div key={char.id} style={{ position: 'relative' }}>
                  <CharacterCard
                    isSelected={characterType === char.id}
                    onClick={() => handleCharacterSelect(char.id)}
                  >
                    <img src={typeof char.image === 'string' ? char.image : char.image.default} alt={char.name} />
                  </CharacterCard>
                  <CharacterName>{char.name}</CharacterName>
                </div>
              ))}
            </CharacterCardsContainer>
          </div>
        }

        <FormContent>
          <img style={{ width: '200px', height: '200px' }} src={MagicCall} alt="" />
          <Form form={form} onFinish={onFinish} >
            <div style={{ color: 'red' }}>{joinFailReason}</div>
            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                block
                size="large"
                style={{ marginTop: 15, fontWeight: 600 }}
              >
                开始魔法视频
              </Button>
            </Form.Item>
          </Form>
        </FormContent>


      </ModalWrapper>
    </>
  );
};

export default JoinRoom;
