# 实时聊天组件功能说明

## 功能概述

在实时音视频页面添加了一个新的聊天组件，用于显示实时的用户输入和AI回复消息。该组件位于页面底部的挂断按钮上方。

## 功能特点

### 1. 消息显示布局
- **用户消息**：显示在右侧，使用蓝色渐变背景
- **AI回复**：显示在左侧，使用深灰色渐变背景
- **消息气泡**：带有尾巴效果，增强视觉体验

### 2. 实时消息处理
- **递增消息**：当 `jsData.definite` 为 `false` 时，前端会覆盖当前句子内容而非追加
- **句子结束**：当 `jsData.definite` 为 `true` 时，将完整句子添加到聊天记录中

### 3. 用户体验优化
- **自动滚动**：新消息出现时自动滚动到底部
- **半透明背景**：使用毛玻璃效果，不遮挡视频内容
- **自定义滚动条**：美观的滚动条样式
- **响应式设计**：适配不同屏幕尺寸

## 技术实现

### 组件结构
```typescript
interface ChatMessage {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: number;
}
```

### 状态管理
- `chatMessages`: 存储已完成的聊天消息
- `currentUserMessage`: 当前正在输入的用户消息
- `currentAIMessage`: 当前正在生成的AI回复

### 样式组件
- `ChatContainer`: 聊天容器，固定定位在底部
- `MessageItem`: 消息项容器，控制消息对齐
- `MessageBubble`: 消息气泡，包含渐变背景和尾巴效果

## 消息处理逻辑

### 用户消息处理
```typescript
if (JS.data[0].userId === userId) {
  if (jsData.definite) {
    // 句子结束，添加到聊天记录
    const newMessage: ChatMessage = {
      id: `user_${Date.now()}`,
      text: JS.data[0].text,
      isUser: true,
      timestamp: Date.now()
    };
    setChatMessages(prev => [...prev, newMessage]);
    setCurrentUserMessage('');
  } else {
    // 递增消息，覆盖当前内容
    setCurrentUserMessage(JS.data[0].text);
  }
}
```

### AI回复处理
```typescript
if (JS.data[0].userId !== userId) {
  if (jsData.definite) {
    // 句子结束，添加到聊天记录
    const newMessage: ChatMessage = {
      id: `ai_${Date.now()}`,
      text: JS.data[0].text,
      isUser: false,
      timestamp: Date.now()
    };
    setChatMessages(prev => [...prev, newMessage]);
    setCurrentAIMessage('');
  } else {
    // 递增消息，覆盖当前内容
    setCurrentAIMessage(JS.data[0].text);
  }
}
```

## 使用方法

1. 组件会自动显示在实时音视频页面底部
2. 当有消息传入时，组件会自动显示
3. 用户消息显示在右侧（蓝色），AI回复显示在左侧（灰色）
4. 消息会自动滚动到最新位置

## 样式特点

- **位置**：固定在页面底部，距离底部120px（在控制栏上方）
- **尺寸**：宽度80%，最大800px，高度200px
- **背景**：半透明黑色背景，带毛玻璃效果
- **边框**：圆角12px，带阴影效果
- **滚动**：自定义滚动条样式

## 注意事项

1. 组件只在有消息时才显示
2. 消息会根据 `definite` 字段决定是追加还是覆盖
3. 自动滚动确保最新消息始终可见
4. 响应式设计适配不同设备
