import React, { useState, useContext, useEffect, useCallback, useRef } from 'react';
import { message } from 'antd';
import styled from 'styled-components';
import {
  MediaType,
  onUserJoinedEvent,
  onUserLeaveEvent,
  PlayerEvent,
  AutoPlayFailedEvent,
} from '@volcengine/rtc';
import { ControlBar, AutoPlayModal } from '../../modules';
import { Context } from '../../context';

import RTCComponent from '../../sdk/rtc-component';
import { RTCClient } from '../../app-interfaces';
import { streamOptions } from './constant';
import config from '../../config';
import MediaPlayer from '../../components/MediaPlayer';
import { getQueryString, removeLoginInfo } from '../../utils';
import { SubtitleMessage, UserMessageEvent } from '@volcengine/rtc';
import { unpack, parseData } from '../../sdk/parseData';
import axios from 'axios';
import { translateDoubao } from '../../sdk/translate_doubao';

interface TranslateText {
  [key: string]: string;
}

interface ChatMessage {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: number;
}

const translateText: TranslateText = {};
let tText = '';
let fullAsrText = '';
const lastTargetText = '';
let lastTText = '';

const Container = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  padding: 8px;
  gap: 8px;
`;

const LeftItem = styled.div`
  width: 30%;
  height: calc(100% - 16px);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
  border-radius: 8px;
`;

const RightItem = styled.div`
  flex: 1;
  height: calc(100% - 16px);
  position: relative;
  background: #000;
  border-radius: 8px;
`;

const SubtitlePop = styled.div`
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  width: 80vw;
  height: 70vh;
  background: #fff;
  display: flex;
  gap: 10px;
`;

const SubtitleBox = styled.div`
  flex: 1;
  overflow-y: auto;
  height: 100%;
  padding: 10px;
`;

const ChatContainer = styled.div`
  position: fixed;
  bottom: 120px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  max-width: 800px;
  height: 200px;
  background: rgba(0, 0, 0, 0.85);
  border-radius: 12px;
  padding: 16px;
  z-index: 999;
  overflow-y: auto;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
  }
`;

const MessageItem = styled.div<{ isUser: boolean }>`
  display: flex;
  justify-content: ${props => props.isUser ? 'flex-end' : 'flex-start'};
  margin-bottom: 8px;
`;

const MessageBubble = styled.div<{ isUser: boolean }>`
  max-width: 70%;
  padding: 10px 14px;
  border-radius: 18px;
  background: ${props => props.isUser ? 'linear-gradient(135deg, #007AFF, #0056CC)' : 'linear-gradient(135deg, #444, #333)'};
  color: white;
  font-size: 14px;
  line-height: 1.4;
  word-wrap: break-word;
  white-space: pre-wrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  position: relative;

  /* 添加消息尾巴效果 */
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border: 6px solid transparent;
    ${props => props.isUser ? `
      right: -12px;
      border-left-color: #007AFF;
    ` : `
      left: -12px;
      border-right-color: #444;
    `}
  }
`;

const Meeting: React.FC<Record<string, unknown>> = () => {
  const { roomId, userId, setJoin, setJoinFailReason } = useContext(Context);
  const [isMicOn, setMicOn] = useState<boolean>(true);
  const [isVideoOn, setVideoOn] = useState<boolean>(true);
  const [isFrontCamera, setIsFrontCamera] = useState<boolean>(true);
  const rtc = useRef<RTCClient>();
  const [autoPlayFailUser, setAutoPlayFailUser] = useState<string[]>([]);
  const playStatus = useRef<{ [key: string]: { audio: boolean; video: boolean } }>({});
  const autoPlayFailUserdRef = useRef<string[]>([]);
  const [userMessage, setUserMessage] = useState<string>('');
  const [asrMessage, setAsrMessage] = useState<string>('');
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [currentUserMessage, setCurrentUserMessage] = useState<string>('');
  const [currentAIMessage, setCurrentAIMessage] = useState<string>('');
  const chatContainerRef = useRef<HTMLDivElement>(null);

  const [remoteStreams, setRemoteStreams] = useState<{
    [key: string]: {
      playerComp: React.ReactNode;
    };
  }>({});

  const leaveRoom = useCallback(
    (refresh: boolean) => {
      const AppId = '671c8711684c980161aa3e72';
      const TaskId = `${AppId}_${roomId}_${userId}`;
      console.log(TaskId);

      axios.post('https://iyuuki.xyz/rtc/api/stop_voice_chat', {
        room_id: roomId,
        user_id: userId,
        task_id: TaskId,
      });

      if (!rtc.current) return;

      // off the event
      rtc.current.removeEventListener();

      rtc.current.leave();
      if (!refresh) {
        setJoin(false);
        removeLoginInfo();
      }

      setAutoPlayFailUser([]);
      setJoinFailReason('');
    },
    [rtc, setJoin]
  );
  // alert(111);

  // useEffect(() => {
  //   if (sessionStorage.getItem('store')) {
  //     const a = sessionStorage.getItem('store');
  //     a && alert(a);
  //   }
  // }, []);
  /**
   * @brief call leaveRoom function when the browser window closes or refreshes
   */
  const leaveFunc = () => {
    leaveRoom(true);
    sessionStorage.setItem('store', JSON.stringify({ test: new Date().toString() }));
  };
  useEffect(() => {
    window.addEventListener('pagehide', leaveFunc);
    return () => {
      leaveRoom(true);
      window.removeEventListener('pagehide', leaveFunc);
    };
  }, [leaveRoom]);

  const handleUserPublishStream = useCallback(
    (stream: { userId: string; mediaType: MediaType }) => {
      const userId = stream.userId;
      if (stream.mediaType & MediaType.VIDEO) {
        if (remoteStreams[userId]) {
          rtc.current?.setRemoteVideoPlayer(userId, `remoteStream_${userId}`);
        }
      }
    },
    [remoteStreams]
  );

  /**
   * @brief remove stream & update remote streams list
   * @param {Event} event
   */
  const handleUserUnpublishStream = (event: { userId: string; mediaType: MediaType }) => {
    const { userId, mediaType } = event;

    if (mediaType & MediaType.VIDEO) {
      rtc.current?.setRemoteVideoPlayer(userId, undefined);
    }
  };

  const handleUserStartVideoCapture = (event: { userId: string }) => {
    const { userId } = event;

    if (remoteStreams[userId]) {
      rtc.current?.setRemoteVideoPlayer(userId, `remoteStream_${userId}`);
    }
  };

  /**
   * Remove the user specified from the room in the local and clear the unused dom
   * @param {*} event
   */
  const handleUserStopVideoCapture = (event: { userId: string }) => {
    const { userId } = event;

    rtc.current?.setRemoteVideoPlayer(userId, undefined);
  };

  const handleUserJoin = (e: onUserJoinedEvent) => {
    console.log('handleUserJoin', e);

    const { userInfo } = e;
    const remoteUserId = userInfo.userId;

    if (Object.keys(remoteStreams).length < 3) {
      if (remoteStreams[remoteUserId]) return;
      remoteStreams[remoteUserId] = {
        playerComp: <MediaPlayer userId={remoteUserId} />,
      };

      setRemoteStreams({
        ...remoteStreams,
      });
    }
  };

  useEffect(() => {
    const streams = Object.keys(remoteStreams);
    const _autoPlayFailUser = autoPlayFailUser.filter(
      (item) => streams.findIndex((stream) => stream === item) !== -1
    );
    setAutoPlayFailUser([..._autoPlayFailUser]);
  }, [remoteStreams]);

  const handleUserLeave = (e: onUserLeaveEvent) => {
    const { userInfo } = e;
    const remoteUserId = userInfo.userId;
    if (remoteStreams[remoteUserId]) {
      delete remoteStreams[remoteUserId];
    }
    setRemoteStreams({
      ...remoteStreams,
    });
  };

  useEffect(() => {
    (async () => {
      if (!roomId || !userId || !rtc.current) return;
      // rtc.current.bindEngineEvents();

      // let token = null;
      // config.tokens.forEach((item) => {
      //   if (item.userId === userId) {
      //     token = item.token;
      //   }
      // });
      const tokenRes: any = await axios.post('https://iyuuki.xyz/rtc/api/gen_token', {
        room_id: roomId,
        user_id: userId,
      });
      if (tokenRes.data.ret !== 0) return;
      const token = tokenRes.data.token;

      console.log(token);

      rtc.current
        .join(token, roomId, userId)
        .then(() =>
          rtc?.current?.createLocalStream(userId, (res: any) => {
            const { code, msg, devicesStatus } = res;
            if (code === -1) {
              alert(msg);
              setMicOn(false);
              setVideoOn(false);
            } else {
              // 视频初始化成功后，获取当前摄像头状态
              setTimeout(() => {
                if (rtc.current) {
                  const cameraStatus = rtc.current.getCurrentCameraStatus();
                  setIsFrontCamera(cameraStatus.isFrontCamera);
                }
              }, 1000); // 延迟1秒确保设备初始化完成
            }
          })
        )
        .catch((err: any) => {
          console.log('err', err);
          leaveRoom(false);
          setJoinFailReason(JSON.stringify(err));
        });
    })();
  }, [roomId, userId, rtc]);

  const changeMicState = useCallback((): void => {
    if (!rtc.current) return;
    rtc.current.changeAudioState(!isMicOn);
    setMicOn(!isMicOn);
  }, [isMicOn, rtc]);

  const changeVideoState = useCallback((): void => {
    if (!rtc.current) return;
    rtc.current.changeVideoState(!isVideoOn);
    setVideoOn(!isVideoOn);
  }, [isVideoOn, rtc]);

  const switchCamera = useCallback(async (): Promise<void> => {
    if (!rtc.current) return;
    try {
      const result = await rtc.current.switchCamera();
      if (result.success) {
        // 使用实际的摄像头状态，而不是简单的取反
        setIsFrontCamera(result.isFrontCamera || false);
        message.success(`切换到${result.isFrontCamera ? '前置' : '后置'}摄像头`);
      } else {
        message.warning('没有检测到多个摄像头');
      }
    } catch (error) {
      console.error('切换摄像头失败:', error);
      message.error('切换摄像头失败');
    }
  }, [rtc]);

  const handleEventError = (e: any, VERTC: any) => {
    if (e.errorCode === VERTC.ErrorCode.DUPLICATE_LOGIN) {
      message.error('你的账号被其他人顶下线了');
      leaveRoom(false);
    }
  };

  const handleAutoPlayFail = (event: AutoPlayFailedEvent) => {
    console.log('handleAutoPlayFail', event.userId, event);
    const { userId, kind } = event;

    let playUser = playStatus.current?.[userId] || {};
    playUser = { ...playUser, [kind]: false };
    playStatus.current[userId] = playUser;

    addFailUser(userId);
  };

  const addFailUser = (userId: string) => {
    const index = autoPlayFailUser.findIndex((item) => item === userId);
    if (index === -1) {
      autoPlayFailUser.push(userId);
    }
    setAutoPlayFailUser([...autoPlayFailUser]);
  };

  const playerFail = (params: { type: 'audio' | 'video'; userId: string }) => {
    const { type, userId } = params;
    let playUser = playStatus.current?.[userId] || {};

    console.log('pause', event);

    playUser = { ...playUser, [type]: false };

    const { audio, video } = playUser;

    if (audio === false || video === false) {
      addFailUser(userId);
    }
  };

  const handlePlayerEvent = (event: PlayerEvent) => {
    const { userId, rawEvent, type } = event;

    console.log('handlePlayerEvent', event, userId, type, rawEvent.type);

    let playUser = playStatus.current?.[userId] || {};

    if (!playStatus.current) return;

    if (rawEvent.type === 'playing') {
      playUser = { ...playUser, [type]: true };
      const { audio, video } = playUser;
      if (audio !== false && video !== false) {
        const _autoPlayFailUser = autoPlayFailUserdRef.current.filter((item) => item !== userId);
        setAutoPlayFailUser([..._autoPlayFailUser]);
      }
    } else if (rawEvent.type === 'pause') {
      playerFail({ userId, type });
    }

    playStatus.current[userId] = playUser;
    console.log('playStatusplayStatusplayStatus', playStatus);
  };

  const handleAutoPlay = () => {
    const users: string[] = autoPlayFailUser;
    console.log('handleAutoPlay autoPlayFailUser', autoPlayFailUser);
    if (users && users.length) {
      users.forEach((user) => {
        rtc.current?.engine.play(user);
      });
    }
    setAutoPlayFailUser([]);
  };

  const handleReceivedSubtitleMessage = (e: SubtitleMessage) => {
    console.log('handleReceivedSubtitleMessage', e);
  };

  const serverType = getQueryString('serverType');
  const lang: any = getQueryString('lang');

  const handleRoomBinaryMessageReceived = async (e: any) => {
    const subtitles = { value: '' };
    const ret = unpack(e.message, subtitles);
    // console.log(e.message);
    if (ret) {
      const JS = JSON.parse(subtitles.value);

      const jsData = JS?.data?.[0];

      if (jsData) {
        if (jsData.text) {
          setAsrMessage(jsData.text + fullAsrText);
        }

        if (jsData.definite && jsData.userId === userId) {
          if (jsData.text) {
            const asrText = jsData.text;
            fullAsrText = asrText + fullAsrText;
            fullAsrText = '\n' + fullAsrText;
            setAsrMessage(fullAsrText);

            if (serverType == '1') {
              const params = new URLSearchParams({ text: asrText, lang: lang }).toString();
              const url = `https://iyuuki.xyz/rtc/api/translate?${params}`; // 服务端接口地址

              // eslint-disable-next-line no-undef
              const eventSource = new EventSource(url);

              // 监听消息
              eventSource.onmessage = (event) => {
                const data = JSON.parse(event.data);
                console.log(data);
                if (data.finish_reason === 'start') {
                  // translateText[asrText] = data.target_text;
                  tText = data.target_text + lastTText;
                  setUserMessage(tText);
                } else if (data.finish_reason === 'stop') {
                  // null
                  tText = '\n' + tText;
                  lastTText = tText;
                  setUserMessage(tText);
                } else {
                  // translateText[asrText] += data.target_text;
                  tText = data.target_text + lastTText;
                  setUserMessage(tText);
                }
              };

              // 监听错误
              eventSource.onerror = (error) => {
                eventSource.close(); // 如果发生错误，关闭连接
              };
            }
          }
        }
      }

      if (JS.tool_calls) {
        // 命中functioncall
        console.log(
          `%c${new Date().toISOString().replace('Z', '') + '命中function_call:'}${JS.tool_calls[0].function.arguments
          }`,
          'background: orange; color: white;'
        );

        // const stringToTLV = (inputString) => {
        //   const type = 'func';
        //   const typeBuffer = new Uint8Array(4);

        //   for (let i = 0; i < type.length; i++) {
        //     typeBuffer[i] = type.charCodeAt(i);
        //   }

        //   const lengthBuffer = new Uint32Array(1);
        //   const valueBuffer = new TextEncoder().encode(inputString);

        //   lengthBuffer[0] = valueBuffer.length;

        //   const tlvBuffer = new Uint8Array(typeBuffer.length + 4 + valueBuffer.length);

        //   tlvBuffer.set(typeBuffer, 0);

        //   tlvBuffer[4] = (lengthBuffer[0] >> 24) & 0xff;
        //   tlvBuffer[5] = (lengthBuffer[0] >> 16) & 0xff;
        //   tlvBuffer[6] = (lengthBuffer[0] >> 8) & 0xff;
        //   tlvBuffer[7] = lengthBuffer[0] & 0xff;

        //   tlvBuffer.set(valueBuffer, 8);

        //   return tlvBuffer.buffer;
        // };

        // const id = JS.tool_calls[0].id;
        // const args = JSON.parse(JS.tool_calls[0].function.arguments);
        // let content = '';

        // if (JS.tool_calls[0].function.name === 'smart_scene_trigger') {
        //   if (args.matched_scene_id) {
        //     const controlRes = await axios.post('https://iyuuki.xyz/rtc/api/tuya_control', {
        //       type: 1,
        //       matched_scene_id: args.matched_scene_id,
        //     });

        //     content = '操作成功';
        //   } else {
        //     content = '未找到该智能场景';
        //   }
        // } else {
        //   if (args.matched_devices) {
        //     const controlRes = await axios.post('https://iyuuki.xyz/rtc/api/tuya_control', {
        //       type: 0,
        //       matched_devices_ids: args.matched_devices_ids,
        //       command: args.command,
        //       command_name: args.command_name,
        //       command_type: args.command_type,
        //       value: args.value,
        //     });
        //     console.log('controlRes', controlRes.data);
        //     if (controlRes.data.ret === 0) content = '操作成功';
        //     if (controlRes.data.ret === 2001) content = '设备离线';
        //     if (controlRes.data.ret === 2008) content = '设备不支持该指令';
        //   } else {
        //     content = '列表中没有该设备';
        //   }
        // }

        // console.log(`%c${content}`, 'background: orange; color: white;');

        // const tlvJs = JSON.stringify({
        //   ToolCallID: id,
        //   Content: content,
        // });
        // const tlv = stringToTLV(tlvJs);
        // // this.engine.sendUserBinaryMessage('rtc', tlv);
        // axios
        //   .post('https://iyuuki.xyz/rtc/api/update_voice_chat', {
        //     user_id: '101',
        //     room_id: '1001',
        //     tool_call_id: id,
        //     content: content,
        //   })
        //   .then(() => {});
      } else {
        // 未命中functioncall
        if (JS?.Stage) {
          console.log(JSON.stringify(JS, null, 2));
        }
        if (JS?.data?.[0] && JS?.data?.[0]?.text) {
          if (JS.data[0].userId !== userId) {
            console.log(
              `%c${new Date().toISOString().replace('Z', '')}智能体回复问答：${JS.data[0].text}`,
              'background: green; color:white ;'
            );

            if (jsData.definite) // 句子结束
            {
              // AI消息句子结束，添加到聊天记录
              const finalText = JS.data[0].text;
              if (finalText.trim()) {
                const newMessage: ChatMessage = {
                  id: `ai_${Date.now()}`,
                  text: finalText,
                  isUser: false,
                  timestamp: Date.now()
                };
                setChatMessages(prev => [...prev, newMessage]);
                setCurrentAIMessage('');
              }
            } else {
              // AI消息递增，覆盖当前消息
              setCurrentAIMessage(JS.data[0].text);
            }
          } else {
            console.log(
              `%c${new Date().toISOString().replace('Z', '')}用户提问：${JS.data[0].text}`,
              'background: #ccc  ; color: black;'
            );

            if (jsData.definite) // 句子结束
            {
              // 用户消息句子结束，添加到聊天记录
              const finalText = JS.data[0].text;
              if (finalText.trim()) {
                const newMessage: ChatMessage = {
                  id: `user_${Date.now()}`,
                  text: finalText,
                  isUser: true,
                  timestamp: Date.now()
                };
                setChatMessages(prev => [...prev, newMessage]);
                setCurrentUserMessage('');
              }
            } else {
              // 用户消息递增，覆盖当前消息
              setCurrentUserMessage(JS.data[0].text);
            }
          }
        }
      }
    }
  };

  useEffect(() => {
    autoPlayFailUserdRef.current = autoPlayFailUser;
  }, [autoPlayFailUser]);

  // 自动滚动到底部
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [chatMessages, currentUserMessage, currentAIMessage]);

  // 测试函数 - 仅用于开发演示
  const addTestMessage = (isUser: boolean) => {
    const testMessages = isUser
      ? ['你好，我想了解一下这个产品', '这个功能怎么使用？', '谢谢你的帮助']
      : ['您好！很高兴为您服务', '这个功能很简单，让我为您详细介绍...', '不客气，还有其他问题吗？'];

    const randomMessage = testMessages[Math.floor(Math.random() * testMessages.length)];
    const newMessage: ChatMessage = {
      id: `${isUser ? 'user' : 'ai'}_${Date.now()}`,
      text: randomMessage,
      isUser,
      timestamp: Date.now()
    };
    setChatMessages(prev => [...prev, newMessage]);
  };

  // 渲染聊天组件
  const renderChatComponent = () => {
    const allMessages = [...chatMessages];

    // 添加当前正在输入的消息
    if (currentUserMessage.trim()) {
      allMessages.push({
        id: 'current_user',
        text: currentUserMessage,
        isUser: true,
        timestamp: Date.now()
      });
    }

    if (currentAIMessage.trim()) {
      allMessages.push({
        id: 'current_ai',
        text: currentAIMessage,
        isUser: false,
        timestamp: Date.now()
      });
    }

    if (allMessages.length === 0) return null;

    return (
      <ChatContainer ref={chatContainerRef}>
        {allMessages.map((message) => (
          <MessageItem key={message.id} isUser={message.isUser}>
            <MessageBubble isUser={message.isUser}>
              {message.text}
            </MessageBubble>
          </MessageItem>
        ))}
      </ChatContainer>
    );
  };

  return (
    <>
      <RTCComponent
        onRef={(ref: any) => (rtc.current = ref)}
        config={{
          ...config,
          roomId,
          uid: '',
        }}
        streamOptions={streamOptions}
        handleUserPublishStream={handleUserPublishStream}
        handleUserUnpublishStream={handleUserUnpublishStream}
        handleUserStartVideoCapture={handleUserStartVideoCapture}
        handleUserStopVideoCapture={handleUserStopVideoCapture}
        handleUserJoin={handleUserJoin}
        handleUserLeave={handleUserLeave}
        handleEventError={handleEventError}
        handleAutoPlayFail={handleAutoPlayFail}
        handlePlayerEvent={handlePlayerEvent}
        handleReceivedSubtitleMessage={handleReceivedSubtitleMessage}
        handleRoomBinaryMessageReceived={handleRoomBinaryMessageReceived}
      />
      <Container>
        {/* 左侧：机器人图片 */}
        <LeftItem>
          <img
            src="/images/robot.png"
            alt="AI Robot"
            style={{
              maxWidth: '80%',
              maxHeight: '80%',
              objectFit: 'contain'
            }}
            onError={(e) => {
              // 如果PNG图片加载失败，尝试加载SVG
              e.currentTarget.src = '/images/robot.svg';
              e.currentTarget.onerror = () => {
                // 如果SVG也失败，显示占位符
                e.currentTarget.style.display = 'none';
                e.currentTarget.parentElement!.innerHTML = '<div style="color: #666; text-align: center; font-size: 48px;">🤖</div>';
              };
            }}
          />
        </LeftItem>

        {/* 右侧：用户视频 */}
        <RightItem>
          <div
            style={{
              width: '100%',
              height: '100%',
              position: 'relative',
              background: '#000',
            }}
            id={'local-player'}
          >
            <span
              style={{
                color: '#fff',
                position: 'absolute',
                bottom: 0,
                right: 0,
                zIndex: 1000,
                maxWidth: 120,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
            >
              {userId}
            </span>
          </div>
        </RightItem>
      </Container>
      {/* <SubtitlePop>
        <SubtitleBox>
          <pre style={{ whiteSpace: 'pre-wrap' }}>{userMessage}</pre>
        </SubtitleBox>
        <div style={{ height: '100%', width: '4px', backgroundColor: '#000' }}></div>
        <SubtitleBox>
          <pre style={{ whiteSpace: 'pre-wrap' }}>{asrMessage}</pre>
        </SubtitleBox>
      </SubtitlePop> */}
      {renderChatComponent()}
      {/* 开发测试按钮 - 仅在开发环境显示 */}
      {process.env.NODE_ENV === 'development' && (
        <div
          style={{
            position: 'fixed',
            top: '20px',
            right: '20px',
            zIndex: 1001,
            display: 'flex',
            gap: '10px'
          }}
        >
          <button
            onClick={() => addTestMessage(true)}
            style={{
              padding: '8px 16px',
              backgroundColor: '#007AFF',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer'
            }}
          >
            添加用户消息
          </button>
          <button
            onClick={() => addTestMessage(false)}
            style={{
              padding: '8px 16px',
              backgroundColor: '#333',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer'
            }}
          >
            添加AI回复
          </button>
        </div>
      )}
      <ControlBar
        RClient={rtc}
        systemConf={[
          {
            moduleName: 'DividerModule',
            moduleProps: {
              width: 2,
              height: 32,
              marginL: 20,
            },
            visible: true,
          },
          {
            moduleName: 'HangUpModule',
            moduleProps: {
              changeHooks: () => {
                leaveRoom(false);
              },
            },
          },
        ]}
        moduleConf={[
          {
            moduleName: 'MicoPhoneControlModule',
            moduleProps: {
              changeHooks: () => changeMicState(),
              isMicOn,
            },
            visible: true,
          },
          {
            moduleName: 'VideoControlModule',
            moduleProps: {
              changeHooks: () => changeVideoState(),
              isVideoOn,
            },
            visible: true,
          },
          {
            moduleName: 'CameraSwitchModule',
            moduleProps: {
              changeHooks: () => switchCamera(),
              isFrontCamera,
            },
            visible: true,
          },
        ]}
      />
      <AutoPlayModal handleAutoPlay={handleAutoPlay} autoPlayFailUser={autoPlayFailUser} />
    </>
  );
};

export default Meeting;
