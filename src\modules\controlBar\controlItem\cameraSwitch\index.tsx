import React from 'react';
import IconBlock from 'src/components/IconBlock';
import { ICameraSwitchProps } from './type';

const CameraSwitch: React.FC<ICameraSwitchProps> = ({ changeHooks, isFrontCamera }) => {
  return (
    <IconBlock onClick={() => changeHooks && changeHooks()} size={28}>
      <div
        style={{
          width: '100%',
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '16px',
          fontWeight: 'bold',
          color: '#333',
          backgroundColor: '#fff',
          borderRadius: '50%',
          border: '2px solid #ddd'
        }}
      >
        {isFrontCamera ? '🔄' : '📷'}
      </div>
    </IconBlock>
  );
};

export default CameraSwitch;
